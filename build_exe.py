"""
打包脚本 - 将项目打包为可执行文件
使用PyInstaller将Python项目打包为exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False


def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False


def clean_build_dirs():
    """清理之前的构建目录"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"🧹 清理目录: {dir_name}")


def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件
import whisper
import os
import site

# 获取whisper包的路径
whisper_path = os.path.dirname(whisper.__file__)
whisper_assets = os.path.join(whisper_path, 'assets')

added_files = [
    ('ffmpeg', 'ffmpeg'),  # 包含整个ffmpeg目录
    ('README.md', '.'),
    ('使用说明.md', '.'),
    (whisper_assets, 'whisper/assets'),  # 包含whisper资源文件
]

# 如果whisper assets目录不存在，尝试从site-packages找
if not os.path.exists(whisper_assets):
    for site_dir in site.getsitepackages():
        alt_whisper_assets = os.path.join(site_dir, 'whisper', 'assets')
        if os.path.exists(alt_whisper_assets):
            added_files.append((alt_whisper_assets, 'whisper/assets'))
            break

# 需要包含的隐藏导入
hiddenimports = [
    'whisper',
    'torch',
    'torchaudio',
    'torchvision', 
    'yt_dlp',
    'ffmpeg',
    'numpy',
    'tiktoken',
    'regex',
    'tqdm',
    'requests',
    'pathlib',
    'datetime',
    'logging',
    'subprocess',
    'urllib.parse',
    'json',
    're',
    'os',
    'sys',
    'shutil',
    'time',
    'threading',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='YouTube音频下载转录工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='YouTube音频下载转录工具',
)
'''
    
    with open('youtube_tool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建规格文件: youtube_tool.spec")


def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "youtube_tool.spec", "--clean"]
        subprocess.run(cmd, check=True)
        print("✅ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False


def create_batch_file():
    """创建批处理文件"""
    batch_content = '''@echo off
chcp 65001 > nul
title YouTube音频下载转录工具
echo.
echo ========================================
echo    YouTube音频下载转录工具
echo ========================================
echo.
"YouTube音频下载转录工具.exe"
pause
'''
    
    dist_dir = Path('dist/YouTube音频下载转录工具')
    if dist_dir.exists():
        batch_file = dist_dir / '启动工具.bat'
        with open(batch_file, 'w', encoding='gbk') as f:
            f.write(batch_content)
        print(f"✅ 创建启动批处理文件: {batch_file}")


def copy_additional_files():
    """复制额外需要的文件到dist目录"""
    dist_dir = Path('dist/YouTube音频下载转录工具')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return
    
    # 创建输出目录
    (dist_dir / 'mp3').mkdir(exist_ok=True)
    (dist_dir / 'txt').mkdir(exist_ok=True)
    (dist_dir / 'temp').mkdir(exist_ok=True)
    
    # 复制说明文件
    files_to_copy = [
        '项目完成报告.md',
        'requirements.txt'
    ]
    
    for file_name in files_to_copy:
        if Path(file_name).exists():
            shutil.copy2(file_name, dist_dir)
            print(f"✅ 复制文件: {file_name}")


def main():
    """主打包流程"""
    print("=" * 60)
    print("    YouTube音频下载转录工具 - 打包脚本")
    print("=" * 60)
    
    # 1. 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("请手动安装PyInstaller: pip install pyinstaller")
            return
    
    # 2. 清理构建目录
    clean_build_dirs()
    
    # 3. 创建规格文件
    create_spec_file()
    
    # 4. 构建可执行文件
    if not build_executable():
        print("构建失败，请检查错误信息")
        return
    
    # 5. 创建批处理文件
    create_batch_file()
    
    # 6. 复制额外文件
    copy_additional_files()
    
    # 7. 显示结果
    dist_dir = Path('dist/YouTube音频下载转录工具')
    if dist_dir.exists():
        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print("=" * 60)
        print(f"📁 输出目录: {dist_dir.absolute()}")
        print(f"🚀 可执行文件: YouTube音频下载转录工具.exe")
        print(f"🎯 启动文件: 启动工具.bat")
        print("\n📋 目录内容:")
        for item in sorted(dist_dir.iterdir()):
            if item.is_file():
                size = item.stat().st_size / (1024*1024)
                print(f"  📄 {item.name} ({size:.1f}MB)")
            else:
                print(f"  📁 {item.name}/")
        
        print("\n🎯 使用方法:")
        print("1. 将整个文件夹复制到目标电脑")
        print("2. 双击'启动工具.bat'或直接运行exe文件")
        print("3. 按照提示输入YouTube URL即可使用")
        print("=" * 60)
    else:
        print("❌ 打包失败，未找到输出目录")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n打包被用户中断")
    except Exception as e:
        print(f"\n打包过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
