# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件
added_files = [
    ('ffmpeg', 'ffmpeg'),  # 包含整个ffmpeg目录
    ('README.md', '.'),
    ('使用说明.md', '.'),
    (r'C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\whisper\assets', 'whisper/assets'),  # Whisper资源文件
]

# 需要包含的隐藏导入
hiddenimports = [
    'whisper',
    'whisper.model',
    'whisper.audio', 
    'whisper.decoding',
    'whisper.tokenizer',
    'torch',
    'torch.nn',
    'torch.nn.functional',
    'torchaudio',
    'torchvision', 
    'yt_dlp',
    'ffmpeg',
    'numpy',
    'tiktoken',
    'regex',
    'tqdm',
    'requests',
    'pathlib',
    'datetime',
    'logging',
    'subprocess',
    'urllib.parse',
    'json',
    're',
    'os',
    'sys',
    'shutil',
    'time',
    'threading',
    'librosa',
    'soundfile',
    'numba',
    'more_itertools',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='YouTube音频下载转录工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='YouTube音频下载转录工具',
)
