# YouTube音频下载和转录工具 - 使用说明

## 🎯 项目完成状态

✅ **项目开发完成！** 所有功能已实现并通过测试。

## 📁 项目文件结构

```
youtube文案下载/
├── main.py              # 🚀 主程序（运行这个文件）
├── downloader.py        # 📥 YouTube下载模块
├── transcriber.py       # 🎤 音频转录模块
├── utils.py            # 🔧 工具函数模块
├── test_system.py      # 🧪 系统测试脚本
├── requirements.txt     # 📦 Python依赖包列表
├── README.md           # 📖 详细说明文档
├── 使用说明.md         # 📋 本文件
├── ffmpeg/             # 🎵 FFmpeg工具（您已放置）
│   └── bin/
│       └── ffmpeg.exe
├── mp3/                # 🎵 音频文件存储目录
├── txt/                # 📝 文本文件存储目录
└── temp/               # 🗂️ 临时文件目录
```

## 🚀 快速开始

### 1. 运行主程序
```bash
python main.py
```

### 2. 输入YouTube视频URL
程序启动后，输入YouTube视频的URL，例如：
- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`

### 3. 选择Whisper模型
程序会提示您选择转录模型：
- **1. tiny** - 最快，准确度较低（推荐测试用）
- **2. base** - 平衡速度和准确度（推荐日常使用）
- **3. small** - 较好准确度
- **4. medium** - 高准确度
- **5. large** - 最高准确度，速度较慢

### 4. 等待处理完成
程序会自动：
1. ✅ 验证URL有效性
2. 📊 获取视频信息
3. 📝 **优先尝试下载字幕**
4. 🎵 如果没有字幕，下载音频文件到 `mp3/` 文件夹
5. 🎤 将音频转录为文字
6. 💾 保存结果到 `txt/` 文件夹

## 📋 程序命令

在程序运行时可以使用：
- `help` 或 `h` - 显示帮助信息
- `info` 或 `i` - 显示系统信息
- `quit` 或 `q` - 退出程序

## 📄 输出文件格式

### 字幕文件（如果视频有字幕）
- **位置**: `txt/` 文件夹
- **命名**: `视频标题_时间戳.txt`
- **内容**: 纯文本字幕

### 转录文件（音频转文字）
- **位置**: `txt/` 文件夹  
- **命名**: `视频标题_transcript_时间戳.txt`
- **内容包括**:
  - 转录基本信息
  - 完整转录文本
  - 分段转录文本（带时间戳）

### 音频文件
- **位置**: `mp3/` 文件夹
- **命名**: `视频标题_时间戳.mp3`
- **格式**: MP3, 192kbps质量

## ✅ 系统测试结果

运行 `python test_system.py` 的测试结果：
- ✅ 模块导入: 通过
- ✅ 系统环境: 通过  
- ✅ 目录结构: 通过
- ✅ FFmpeg: 通过（使用本地ffmpeg/bin/ffmpeg.exe）
- ✅ URL验证: 通过
- ✅ Whisper模型: 通过

**🎉 所有测试通过！系统准备就绪。**

## 🔧 技术特点

1. **智能处理流程**: 优先下载字幕，节省时间和资源
2. **本地FFmpeg**: 使用项目内的FFmpeg，无需系统安装
3. **多模型支持**: 支持tiny到large等多种Whisper模型
4. **自动文件管理**: 智能命名，避免文件冲突
5. **完善错误处理**: 网络异常、文件权限等问题的处理
6. **进度显示**: 实时显示下载和转录进度
7. **中文优化**: 优先下载中文字幕，转录时指定中文语言

## 📝 使用示例

```
请输入YouTube视频URL (或输入命令): https://www.youtube.com/watch?v=example

可用的Whisper模型:
1. tiny   - 最快，准确度较低
2. base   - 平衡速度和准确度 (推荐)
3. small  - 较好准确度
4. medium - 高准确度
5. large  - 最高准确度，速度较慢

选择模型 (1-5) [默认: 2]: 2

开始处理视频 (使用 base 模型)...
========================================
正在验证URL...
正在获取视频信息...
视频标题: 示例视频标题
时长: 5分30秒
上传者: 示例频道

正在尝试下载字幕...
✓ 字幕下载成功: 示例视频标题_20241225_143022.txt

========================================
处理结果:
✓ 处理成功!
方法: 字幕下载
字幕文件: txt/示例视频标题_20241225_143022.txt
处理时间: 15秒
========================================
```

## 🎯 项目优势

- **完全免费**: 使用开源工具，无需API费用
- **离线工作**: 除了下载视频，其他处理都在本地完成
- **高质量转录**: 使用OpenAI Whisper，准确度高
- **用户友好**: 简洁的命令行界面
- **智能优化**: 优先字幕，备选转录
- **文件管理**: 自动整理，便于查找

## 🚀 现在就开始使用吧！

运行命令：
```bash
python main.py
```

享受高效的YouTube内容提取体验！ 🎉
