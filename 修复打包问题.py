"""
修复PyInstaller打包Whisper时缺少资源文件的问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def find_whisper_assets():
    """查找Whisper资源文件位置"""
    try:
        import whisper
        whisper_path = Path(whisper.__file__).parent
        assets_path = whisper_path / 'assets'
        
        print(f"Whisper安装路径: {whisper_path}")
        print(f"资源文件路径: {assets_path}")
        
        if assets_path.exists():
            print("✅ 找到Whisper资源文件")
            files = list(assets_path.glob('*'))
            for file in files:
                print(f"  - {file.name}")
            return str(assets_path)
        else:
            print("❌ 未找到Whisper资源文件")
            return None
            
    except ImportError:
        print("❌ Whisper未安装")
        return None


def create_fixed_spec_file():
    """创建修复后的spec文件"""
    assets_path = find_whisper_assets()
    if not assets_path:
        print("无法创建spec文件，Whisper资源文件未找到")
        return False
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件
added_files = [
    ('ffmpeg', 'ffmpeg'),  # 包含整个ffmpeg目录
    ('README.md', '.'),
    ('使用说明.md', '.'),
    (r'{assets_path}', 'whisper/assets'),  # Whisper资源文件
]

# 需要包含的隐藏导入
hiddenimports = [
    'whisper',
    'whisper.model',
    'whisper.audio', 
    'whisper.decoding',
    'whisper.tokenizer',
    'torch',
    'torch.nn',
    'torch.nn.functional',
    'torchaudio',
    'torchvision', 
    'yt_dlp',
    'ffmpeg',
    'numpy',
    'tiktoken',
    'regex',
    'tqdm',
    'requests',
    'pathlib',
    'datetime',
    'logging',
    'subprocess',
    'urllib.parse',
    'json',
    're',
    'os',
    'sys',
    'shutil',
    'time',
    'threading',
    'librosa',
    'soundfile',
    'numba',
    'more_itertools',
]

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='YouTube音频下载转录工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='YouTube音频下载转录工具',
)
'''
    
    with open('youtube_tool_fixed.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建修复后的spec文件: youtube_tool_fixed.spec")
    return True


def build_with_fixed_spec():
    """使用修复后的spec文件构建"""
    try:
        print("开始使用修复后的spec文件构建...")
        cmd = [sys.executable, "-m", "PyInstaller", "youtube_tool_fixed.spec", "--clean"]
        subprocess.run(cmd, check=True)
        print("✅ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False


def copy_whisper_assets_manually():
    """手动复制Whisper资源文件到dist目录"""
    assets_path = find_whisper_assets()
    if not assets_path:
        return False
    
    dist_dir = Path('dist/YouTube音频下载转录工具')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 创建whisper/assets目录
    target_assets_dir = dist_dir / 'whisper' / 'assets'
    target_assets_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制资源文件
    assets_source = Path(assets_path)
    for file in assets_source.glob('*'):
        if file.is_file():
            shutil.copy2(file, target_assets_dir)
            print(f"✅ 复制资源文件: {file.name}")
    
    return True


def create_fixed_batch_file():
    """创建修复后的批处理文件"""
    assets_path = find_whisper_assets()
    if not assets_path:
        return False
    
    batch_content = f'''@echo off
chcp 65001 > nul
title YouTube工具打包脚本（修复版）

echo ========================================
echo    YouTube音频下载转录工具 - 修复打包
echo ========================================
echo.

echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 正在安装PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ PyInstaller安装失败
    pause
    exit /b 1
)

echo.
echo 正在清理旧的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__
if exist *.spec del *.spec

echo.
echo Whisper资源文件路径: {assets_path}

echo.
echo 开始修复打包...
pyinstaller --onedir ^
    --name="YouTube音频下载转录工具" ^
    --add-data="ffmpeg;ffmpeg" ^
    --add-data="README.md;." ^
    --add-data="使用说明.md;." ^
    --add-data="{assets_path};whisper/assets" ^
    --collect-data=whisper ^
    --collect-binaries=whisper ^
    --hidden-import=whisper ^
    --hidden-import=whisper.model ^
    --hidden-import=whisper.audio ^
    --hidden-import=whisper.decoding ^
    --hidden-import=whisper.tokenizer ^
    --hidden-import=torch ^
    --hidden-import=torch.nn ^
    --hidden-import=torch.nn.functional ^
    --hidden-import=torchaudio ^
    --hidden-import=yt_dlp ^
    --hidden-import=ffmpeg ^
    --hidden-import=numpy ^
    --hidden-import=tiktoken ^
    --hidden-import=regex ^
    --hidden-import=tqdm ^
    --hidden-import=requests ^
    --hidden-import=librosa ^
    --hidden-import=soundfile ^
    --hidden-import=numba ^
    --hidden-import=more_itertools ^
    --console ^
    --clean ^
    main.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 正在创建输出目录结构...
mkdir "dist\\YouTube音频下载转录工具\\mp3" 2>nul
mkdir "dist\\YouTube音频下载转录工具\\txt" 2>nul
mkdir "dist\\YouTube音频下载转录工具\\temp" 2>nul

echo.
echo 正在复制额外文件...
copy "项目完成报告.md" "dist\\YouTube音频下载转录工具\\" 2>nul
copy "requirements.txt" "dist\\YouTube音频下载转录工具\\" 2>nul

echo.
echo 正在创建启动批处理文件...
(
echo @echo off
echo chcp 65001 ^> nul
echo title YouTube音频下载转录工具
echo echo.
echo echo ========================================
echo echo    YouTube音频下载转录工具
echo echo ========================================
echo echo.
echo "YouTube音频下载转录工具.exe"
echo pause
) > "dist\\YouTube音频下载转录工具\\启动工具.bat"

echo.
echo ========================================
echo 🎉 修复打包完成！
echo ========================================
echo 📁 输出目录: dist\\YouTube音频下载转录工具\\
echo 🚀 可执行文件: YouTube音频下载转录工具.exe
echo 🎯 启动文件: 启动工具.bat
echo.
echo 🎯 使用方法:
echo 1. 将整个文件夹复制到目标电脑
echo 2. 双击'启动工具.bat'或直接运行exe文件
echo 3. 按照提示输入YouTube URL即可使用
echo ========================================

pause'''
    
    with open('修复打包命令.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ 创建修复后的批处理文件: 修复打包命令.bat")
    return True


def main():
    """主修复流程"""
    print("=" * 60)
    print("    YouTube音频下载转录工具 - 打包问题修复")
    print("=" * 60)
    
    # 1. 查找Whisper资源文件
    assets_path = find_whisper_assets()
    if not assets_path:
        print("请先安装Whisper: pip install openai-whisper")
        return
    
    # 2. 创建修复后的spec文件
    if create_fixed_spec_file():
        print("✅ spec文件创建成功")
    
    # 3. 创建修复后的批处理文件
    if create_fixed_batch_file():
        print("✅ 批处理文件创建成功")
    
    # 4. 提供解决方案
    print("\n" + "=" * 60)
    print("🔧 解决方案:")
    print("=" * 60)
    print("方案1: 运行修复后的批处理文件")
    print("  双击: 修复打包命令.bat")
    print()
    print("方案2: 使用修复后的spec文件")
    print("  命令: pyinstaller youtube_tool_fixed.spec --clean")
    print()
    print("方案3: 手动复制资源文件（如果已经打包过）")
    print("  运行: python 修复打包问题.py --copy-assets")
    print("=" * 60)


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--copy-assets':
        copy_whisper_assets_manually()
    else:
        main()
