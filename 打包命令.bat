@echo off
chcp 65001 > nul
title YouTube工具打包脚本

echo ========================================
echo    YouTube音频下载转录工具 - 打包脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 正在安装PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo ❌ PyInstaller安装失败
    pause
    exit /b 1
)

echo.
echo 正在清理旧的构建文件...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__
if exist *.spec del *.spec

echo.
echo 正在查找Whisper资源文件...
python -c "import whisper, os; print('Whisper路径:', os.path.dirname(whisper.__file__))"

echo.
echo 开始打包...
pyinstaller --onedir ^
    --name="YouTube音频下载转录工具" ^
    --add-data="ffmpeg;ffmpeg" ^
    --add-data="README.md;." ^
    --add-data="使用说明.md;." ^
    --collect-data=whisper ^
    --collect-binaries=whisper ^
    --hidden-import=whisper ^
    --hidden-import=whisper.model ^
    --hidden-import=whisper.audio ^
    --hidden-import=whisper.decoding ^
    --hidden-import=whisper.tokenizer ^
    --hidden-import=torch ^
    --hidden-import=torch.nn ^
    --hidden-import=torch.nn.functional ^
    --hidden-import=torchaudio ^
    --hidden-import=yt_dlp ^
    --hidden-import=ffmpeg ^
    --hidden-import=numpy ^
    --hidden-import=tiktoken ^
    --hidden-import=regex ^
    --hidden-import=tqdm ^
    --hidden-import=requests ^
    --hidden-import=librosa ^
    --hidden-import=soundfile ^
    --console ^
    --clean ^
    main.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 正在创建输出目录结构...
mkdir "dist\YouTube音频下载转录工具\mp3" 2>nul
mkdir "dist\YouTube音频下载转录工具\txt" 2>nul
mkdir "dist\YouTube音频下载转录工具\temp" 2>nul

echo.
echo 正在复制额外文件...
copy "项目完成报告.md" "dist\YouTube音频下载转录工具\" 2>nul
copy "requirements.txt" "dist\YouTube音频下载转录工具\" 2>nul

echo.
echo 正在创建启动批处理文件...
(
echo @echo off
echo chcp 65001 ^> nul
echo title YouTube音频下载转录工具
echo echo.
echo echo ========================================
echo echo    YouTube音频下载转录工具
echo echo ========================================
echo echo.
echo "YouTube音频下载转录工具.exe"
echo pause
) > "dist\YouTube音频下载转录工具\启动工具.bat"

echo.
echo ========================================
echo 🎉 打包完成！
echo ========================================
echo 📁 输出目录: dist\YouTube音频下载转录工具\
echo 🚀 可执行文件: YouTube音频下载转录工具.exe
echo 🎯 启动文件: 启动工具.bat
echo.
echo 🎯 使用方法:
echo 1. 将整个文件夹复制到目标电脑
echo 2. 双击'启动工具.bat'或直接运行exe文件
echo 3. 按照提示输入YouTube URL即可使用
echo ========================================

pause
