"""
测试打包后的exe文件是否能正常工作
"""

import subprocess
import os
from pathlib import Path


def test_exe():
    """测试exe文件"""
    exe_path = Path("dist/YouTube音频下载转录工具/YouTube音频下载转录工具.exe")
    
    if not exe_path.exists():
        print("❌ exe文件不存在")
        return False
    
    print(f"✅ 找到exe文件: {exe_path}")
    
    # 测试exe是否能启动
    try:
        print("正在测试exe启动...")
        # 使用timeout避免程序一直等待用户输入
        result = subprocess.run(
            [str(exe_path)],
            input="quit\n",  # 发送quit命令退出
            text=True,
            capture_output=True,
            timeout=30,
            cwd=exe_path.parent
        )
        
        if "YouTube 音频下载和转录工具" in result.stdout:
            print("✅ exe文件启动成功")
            return True
        else:
            print("❌ exe文件启动异常")
            print("输出:", result.stdout)
            print("错误:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️  exe文件启动超时（可能正常，程序在等待用户输入）")
        return True
    except Exception as e:
        print(f"❌ exe文件测试失败: {e}")
        return False


def check_whisper_assets():
    """检查Whisper资源文件"""
    assets_dir = Path("dist/YouTube音频下载转录工具/whisper/assets")
    
    if not assets_dir.exists():
        print("❌ Whisper资源目录不存在")
        return False
    
    required_files = ['mel_filters.npz', 'gpt2.tiktoken', 'multilingual.tiktoken']
    missing_files = []
    
    for file_name in required_files:
        file_path = assets_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name} 存在")
        else:
            print(f"❌ {file_name} 缺失")
            missing_files.append(file_name)
    
    return len(missing_files) == 0


def main():
    """主测试函数"""
    print("=" * 50)
    print("测试打包后的exe文件")
    print("=" * 50)
    
    # 1. 检查Whisper资源文件
    print("1. 检查Whisper资源文件...")
    if check_whisper_assets():
        print("✅ Whisper资源文件检查通过")
    else:
        print("❌ Whisper资源文件检查失败")
        print("请运行: python 修复打包问题.py --copy-assets")
        return
    
    # 2. 测试exe文件
    print("\n2. 测试exe文件...")
    if test_exe():
        print("✅ exe文件测试通过")
    else:
        print("❌ exe文件测试失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！exe文件应该可以正常使用了")
    print("=" * 50)
    print("现在可以:")
    print("1. 运行 dist/YouTube音频下载转录工具/启动工具.bat")
    print("2. 或直接运行 YouTube音频下载转录工具.exe")
    print("3. 将整个文件夹复制到其他电脑使用")


if __name__ == "__main__":
    main()
